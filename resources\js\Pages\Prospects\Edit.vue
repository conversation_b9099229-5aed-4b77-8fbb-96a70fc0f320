<script setup>
import { ref, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import SvgLink from '@/Components/ActionLink.vue';
import TextInput from '@/Components/TextInput.vue';
import { Head, useForm } from '@inertiajs/vue3';

const props = defineProps({
  prospect: Object,
  users: Array,
  statusOptions: Array,
  priorityOptions: Array,
  leadSourceOptions: Array,
  budgetRangeOptions: Array,
});

const form = useForm({
  first_name: props.prospect.first_name || '',
  last_name: props.prospect.last_name || '',
  email: props.prospect.email || '',
  phone: props.prospect.phone || '',
  company: props.prospect.company || '',
  position: props.prospect.position || '',
  country: props.prospect.country || '',
  city: props.prospect.city || '',
  lead_source: props.prospect.lead_source || 'other',
  lead_source_details: props.prospect.lead_source_details || '',
  linkedin_url: props.prospect.linkedin_url || '',
  website_url: props.prospect.website_url || '',
  company_website: props.prospect.company_website || '',
  status: props.prospect.status || 'new',
  priority: props.prospect.priority || 'medium',
  score: props.prospect.score || 0,
  initial_conversation: props.prospect.initial_conversation || '',
  notes: props.prospect.notes || '',
  next_follow_up_at: props.prospect.next_follow_up_at ? props.prospect.next_follow_up_at.slice(0, 16) : '',
  estimated_budget: props.prospect.estimated_budget || '',
  project_type: props.prospect.project_type || '',
  requirements: props.prospect.requirements || '',
  budget_range: props.prospect.budget_range || '',
  assigned_to: props.prospect.assigned_to || '',
});

const submit = () => {
  form.patch(route('prospects.update', props.prospect.id));
};

const formatLeadSourceLabel = (source) => {
  return source.charAt(0).toUpperCase() + source.slice(1).replace('_', ' ');
};

const formatBudgetRangeLabel = (range) => {
  const labels = {
    'under_1k': 'Under $1,000',
    '1k_5k': '$1,000 - $5,000',
    '5k_10k': '$5,000 - $10,000',
    '10k_25k': '$10,000 - $25,000',
    '25k_50k': '$25,000 - $50,000',
    'over_50k': 'Over $50,000',
  };
  return labels[range] || range;
};
</script>

<template>
  <Head title="Prospects" />

  <AdminLayout>


    <div class="animate-top">
        <div class="bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <h2 class="text-2xl font-semibold leading-7 text-gray-900 mb-10">Edit Prospects</h2>
            <form @submit.prevent="submit" class="space-y-6">

              <!-- Personal Information -->
              <div class="">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <InputLabel for="first_name" value="First Name *" />
                    <TextInput
                      id="first_name"
                      v-model="form.first_name"
                      type="text"
                      class="mt-1 block w-full"
                      required
                    />
                    <InputError class="mt-2" :message="form.errors.first_name" />
                  </div>

                  <div>
                    <InputLabel for="last_name" value="Last Name *" />
                    <TextInput
                      id="last_name"
                      v-model="form.last_name"
                      type="text"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.last_name" />
                  </div>

                  <div>
                    <InputLabel for="email" value="Email *" />
                    <TextInput
                      id="email"
                      v-model="form.email"
                      type="email"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.email" />
                  </div>

                  <div>
                    <InputLabel for="phone" value="Phone" />
                    <TextInput
                      id="phone"
                      v-model="form.phone"
                      type="text"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.phone" />
                  </div>
                </div>
              </div>

              <!-- Company Information -->
              <div class="">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <InputLabel for="company" value="Company" />
                    <TextInput
                      id="company"
                      v-model="form.company"
                      type="text"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.company" />
                  </div>

                  <div>
                    <InputLabel for="position" value="Position" />
                    <TextInput
                      id="position"
                      v-model="form.position"
                      type="text"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.position" />
                  </div>

                  <div>
                    <InputLabel for="country" value="Country" />
                    <TextInput
                      id="country"
                      v-model="form.country"
                      type="text"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.country" />
                  </div>

                  <div>
                    <InputLabel for="city" value="City" />
                    <TextInput
                      id="city"
                      v-model="form.city"
                      type="text"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.city" />
                  </div>
                </div>
              </div>

              <!-- Lead Source & URLs -->
              <div class="">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Lead Source & URLs</h3>
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                  <div class="md:col-span-2">
                    <InputLabel for="lead_source" value="Lead Source *" />
                    <select
                      id="lead_source"
                      v-model="form.lead_source"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    >
                      <option v-for="source in leadSourceOptions" :key="source" :value="source">
                        {{ formatLeadSourceLabel(source) }}
                      </option>
                    </select>
                    <InputError class="mt-2" :message="form.errors.lead_source" />
                  </div>

                  <div class="md:col-span-2">
                    <InputLabel for="lead_source_details" value="Lead Source Details" />
                    <TextInput
                      id="lead_source_details"
                      v-model="form.lead_source_details"
                      type="text"
                      class="mt-1 block w-full"
                      placeholder="Additional details about the source"
                    />
                    <InputError class="mt-2" :message="form.errors.lead_source_details" />
                  </div>

                  <div class="md:col-span-2">
                    <InputLabel for="linkedin_url" value="LinkedIn URL" />
                    <TextInput
                      id="linkedin_url"
                      v-model="form.linkedin_url"
                      type="url"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.linkedin_url" />
                  </div>

                  <div class="md:col-span-3">
                    <InputLabel for="website_url" value="Personal Website" />
                    <TextInput
                      id="website_url"
                      v-model="form.website_url"
                      type="url"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.website_url" />
                  </div>

                  <div class="md:col-span-3">
                    <InputLabel for="company_website" value="Company Website" />
                    <TextInput
                      id="company_website"
                      v-model="form.company_website"
                      type="url"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.company_website" />
                  </div>
                </div>
              </div>

              <!-- Status & Priority -->
              <div class="">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Status & Priority</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <InputLabel for="status" value="Status *" />
                    <select
                      id="status"
                      v-model="form.status"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    >
                      <option v-for="status in statusOptions" :key="status" :value="status">
                        {{ status.charAt(0).toUpperCase() + status.slice(1) }}
                      </option>
                    </select>
                    <InputError class="mt-2" :message="form.errors.status" />
                  </div>

                  <div>
                    <InputLabel for="priority" value="Priority *" />
                    <select
                      id="priority"
                      v-model="form.priority"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    >
                      <option v-for="priority in priorityOptions" :key="priority" :value="priority">
                        {{ priority.charAt(0).toUpperCase() + priority.slice(1) }}
                      </option>
                    </select>
                    <InputError class="mt-2" :message="form.errors.priority" />
                  </div>

                  <div>
                    <InputLabel for="score" value="Score (0-100)" />
                    <TextInput
                      id="score"
                      v-model="form.score"
                      type="number"
                      min="0"
                      max="100"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.score" />
                  </div>
                </div>
              </div>

              <!-- Project & Budget Information -->
              <div class="">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Project & Budget Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <InputLabel for="project_type" value="Project Type" />
                    <TextInput
                      id="project_type"
                      v-model="form.project_type"
                      type="text"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.project_type" />
                  </div>

                  <div>
                    <InputLabel for="estimated_budget" value="Estimated Budget ($)" />
                    <TextInput
                      id="estimated_budget"
                      v-model="form.estimated_budget"
                      type="number"
                      step="0.01"
                      min="0"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.estimated_budget" />
                  </div>

                  <div>
                    <InputLabel for="budget_range" value="Budget Range" />
                    <select
                      id="budget_range"
                      v-model="form.budget_range"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    >
                      <option value="">Select budget range</option>
                      <option v-for="range in budgetRangeOptions" :key="range" :value="range">
                        {{ formatBudgetRangeLabel(range) }}
                      </option>
                    </select>
                    <InputError class="mt-2" :message="form.errors.budget_range" />
                  </div>

                  <div>
                    <InputLabel for="assigned_to" value="Assign To" />
                    <select
                      id="assigned_to"
                      v-model="form.assigned_to"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    >
                      <option value="">Select user</option>
                      <option v-for="user in users" :key="user.id" :value="user.id">
                        {{ user.name }}
                      </option>
                    </select>
                    <InputError class="mt-2" :message="form.errors.assigned_to" />
                  </div>

                  <div class="md:col-span-4">
                    <InputLabel for="requirements" value="Requirements" />
                    <textarea
                      id="requirements"
                      v-model="form.requirements"
                      rows="3"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                      placeholder="Project requirements and details"
                    ></textarea>
                    <InputError class="mt-2" :message="form.errors.requirements" />
                  </div>
                </div>
              </div>

              <!-- Conversation & Notes -->
              <div class="">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Conversation & Notes</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <InputLabel for="initial_conversation" value="Initial Conversation" />
                    <textarea
                      id="initial_conversation"
                      v-model="form.initial_conversation"
                      rows="3"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                      placeholder="Record of initial conversation"
                    ></textarea>
                    <InputError class="mt-2" :message="form.errors.initial_conversation" />
                  </div>

                  <div>
                    <InputLabel for="notes" value="Notes" />
                    <textarea
                      id="notes"
                      v-model="form.notes"
                      rows="3"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                      placeholder="Additional notes about the prospect"
                    ></textarea>
                    <InputError class="mt-2" :message="form.errors.notes" />
                  </div>

                  <div>
                    <InputLabel for="next_follow_up_at" value="Next Follow-up Date" />
                    <TextInput
                      id="next_follow_up_at"
                      v-model="form.next_follow_up_at"
                      type="datetime-local"
                      class="mt-1 block w-full"
                    />
                    <InputError class="mt-2" :message="form.errors.next_follow_up_at" />
                  </div>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="flex mt-6 items-center justify-between">
                <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('prospects.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Back</button>
                            </template>
                    </SvgLink>
                    <PrimaryButton :disabled="form.processing">Update</PrimaryButton>
                    </div>
                </div>
            </form>
        </div>
    </div>
  </AdminLayout>
</template>

<style scoped>
select {
    padding: 6px  !important;
}
</style>
