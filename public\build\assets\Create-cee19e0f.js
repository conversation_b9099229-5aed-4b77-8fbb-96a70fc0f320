import{T as f,o as g,c as _,a as s,u as t,w as n,F as b,Z as h,b as o,h as y,k as d,y as c,g as v}from"./app-ab313c9b.js";import{_ as w,b as x}from"./AdminLayout-e03e3765.js";import{_ as a}from"./InputLabel-96626de3.js";import{_ as m}from"./TextInput-3a192a65.js";import{_ as i}from"./InputError-fb109721.js";import{P as j}from"./PrimaryButton-a46c6f01.js";import"./_plugin-vue_export-helper-c27b6911.js";const V={class:"items-start"},k=o("div",{class:"flex justify-between items-center mb-6"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Portfolio Project")],-1),P={class:"bg-white rounded-lg shadow p-6"},S=["onSubmit"],U={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},B={class:"flex mt-6 items-center justify-between"},N={class:"ml-auto flex items-center justify-end gap-x-6"},T=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),A={__name:"Create",setup($){const e=f({project_name:"",url:"",description:"",technology:""}),u=()=>{e.post(route("portfolios.store"),{onSuccess:()=>e.reset()})};return(p,l)=>(g(),_(b,null,[s(t(h),{title:"Portfolio"}),s(w,null,{default:n(()=>[o("div",V,[k,o("div",P,[o("form",{onSubmit:y(u,["prevent"]),class:"space-y-6"},[o("div",U,[o("div",null,[s(a,{for:"project_name",value:"Project Name *"}),s(m,{id:"project_name",modelValue:t(e).project_name,"onUpdate:modelValue":l[0]||(l[0]=r=>t(e).project_name=r),type:"text",class:"mt-1 block w-full",required:"",autofocus:"",placeholder:"Enter project name"},null,8,["modelValue"]),s(i,{class:"mt-2",message:t(e).errors.project_name},null,8,["message"])]),o("div",null,[s(a,{for:"url",value:"Project URL"}),s(m,{id:"url",modelValue:t(e).url,"onUpdate:modelValue":l[1]||(l[1]=r=>t(e).url=r),type:"url",class:"mt-1 block w-full",placeholder:"https://example.com"},null,8,["modelValue"]),s(i,{class:"mt-2",message:t(e).errors.url},null,8,["message"])]),o("div",null,[s(a,{for:"description",value:"Description"}),d(o("textarea",{id:"description","onUpdate:modelValue":l[2]||(l[2]=r=>t(e).description=r),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Describe your project..."},null,512),[[c,t(e).description]]),s(i,{class:"mt-2",message:t(e).errors.description},null,8,["message"])]),o("div",null,[s(a,{for:"technology",value:"Technologies Used"}),d(o("textarea",{id:"technology","onUpdate:modelValue":l[3]||(l[3]=r=>t(e).technology=r),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"e.g., Laravel, Vue.js, MySQL, Tailwind CSS"},null,512),[[c,t(e).technology]]),s(i,{class:"mt-2",message:t(e).errors.technology},null,8,["message"])])]),o("div",B,[o("div",N,[s(x,{href:p.route("portfolios.index")},{svg:n(()=>[T]),_:1},8,["href"]),s(j,{disabled:t(e).processing},{default:n(()=>[v("Save")]),_:1},8,["disabled"])])])],40,S)])])]),_:1})],64))}};export{A as default};
