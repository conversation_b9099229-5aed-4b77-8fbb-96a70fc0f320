import{_ as o}from"./AdminLayout-e03e3765.js";import i from"./DeleteUserForm-fa945291.js";import m from"./UpdatePasswordForm-5b1e1bdc.js";import r from"./UpdateProfileInformationForm-42a8c5ab.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-ab313c9b.js";import"./DangerButton-a8df982e.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-fb109721.js";import"./InputLabel-96626de3.js";import"./Modal-b5ccb299.js";/* empty css                                                              */import"./SecondaryButton-cda7d669.js";import"./TextInput-3a192a65.js";import"./PrimaryButton-a46c6f01.js";import"./TextArea-1f11cb2e.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
