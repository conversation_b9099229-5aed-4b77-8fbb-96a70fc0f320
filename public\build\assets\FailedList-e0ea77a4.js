import{T,r as i,o as n,c,a as o,u as _,w as a,F as R,Z as W,b as e,k as X,y as Y,d as ee,e as te,f as N,g as m,t as d,h as se}from"./app-ab313c9b.js";import{_ as oe,a as le}from"./AdminLayout-e03e3765.js";import{_ as S}from"./SecondaryButton-cda7d669.js";import{D as ae}from"./DangerButton-a8df982e.js";import{P as O}from"./PrimaryButton-a46c6f01.js";import{M as p}from"./Modal-b5ccb299.js";import{_ as ie}from"./Pagination-bae10c2c.js";import{_ as ne}from"./SearchableDropdownNew-ed82eab3.js";import{_ as w}from"./InputLabel-96626de3.js";import{_ as de}from"./TextInput-3a192a65.js";import{_ as re}from"./InputError-fb109721.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const ce={class:"animate-top"},ue={class:"sm:flex sm:items-center"},he=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Failed Emails")],-1),me={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},_e={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},pe=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ge={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},xe={class:"flex mb-2"},fe=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),ve={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},we={class:"sm:col-span-4"},ye={class:"relative mt-2"},be={class:"mt-8 overflow-x-auto sm:rounded-lg"},ke={class:"shadow sm:rounded-lg"},Ce={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Me=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[e("tr",{class:"border-b-2"},[e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Recipient"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Sequence"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Subject"),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACTION")])],-1),Ee={key:0},je={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},Be={class:"text-xs text-red-600"},Se={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},Ve={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},qe=["onClick"],Le={class:"items-center px-4 py-2.5"},$e={class:"flex items-center justify-start gap-4"},ze=e("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Fe=["onClick"],Ae=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),De=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Te=[Ae,De],Re=["onClick"],Ne=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"})],-1),Oe=e("span",{class:"text-sm text-gray-700 leading-5"}," Reschedule ",-1),Ie=[Ne,Oe],Ue=["onClick"],He=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Pe=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Ze=[He,Pe],Ge={key:1},Je=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No failed emails found. ")],-1),Ke=[Je],Qe={class:"p-6"},We=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this failed email? ",-1),Xe={class:"mt-6 flex justify-end"},Ye={class:"p-6"},et=e("h2",{class:"text-lg font-medium text-gray-900"}," Reschedule Email ",-1),tt=e("p",{class:"mt-2 text-sm text-gray-600"}," This will reset the email status and attempt to send it again. Are you sure you want to continue? ",-1),st={class:"mt-6 flex justify-end"},ot={class:"p-0 max-h-[80vh] flex flex-col relative"},lt={class:"sticky top-0 bg-white z-10 px-4 py-3 border-b"},at=e("h2",{class:"text-base font-medium text-gray-900"},"Email Content",-1),it=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),nt=[it],dt={class:"px-4 pb-4 overflow-y-auto"},rt=["innerHTML"],ct={class:"p-0 max-h-[80vh] flex flex-col relative"},ut={class:"sticky top-0 bg-white z-10 px-4 py-3 border-b"},ht=e("h2",{class:"text-base font-medium text-red-600"},"Error Details",-1),mt=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),_t=[mt],pt={class:"px-4 py-4 overflow-y-auto"},gt={class:"bg-red-50 border border-red-200 rounded p-4 text-red-800"},xt={class:"p-0 max-h-[80vh] flex flex-col relative"},ft={class:"sticky top-0 bg-white z-10 px-4 py-3 border-b"},vt=e("h2",{class:"text-base font-medium text-gray-900"},"Edit Failed Email",-1),wt=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),yt=[wt],bt={class:"px-6 py-4 overflow-y-auto"},kt=["onSubmit"],Ct={class:"mb-4"},Mt=e("p",{class:"mt-2 text-xs text-gray-500"}," Correct the email address to fix the delivery issue. ",-1),Et={key:0,class:"mb-4"},jt={class:"bg-gray-50 rounded-md p-4 border border-gray-200"},Bt={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},St=e("p",{class:"text-gray-500"},"Sequence:",-1),Vt={class:"font-medium"},qt=e("p",{class:"text-gray-500"},"Subject:",-1),Lt={class:"font-medium"},$t=e("p",{class:"text-gray-500"},"Lead:",-1),zt={class:"font-medium"},Ft=e("div",{class:"mb-4"},[e("div",{class:"bg-blue-50 border border-blue-200 rounded p-3 text-blue-800 text-sm"},[e("div",{class:"flex items-start"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("div",null,[e("p",{class:"font-medium"},"Automatic Rescheduling"),e("p",null,"This email will be automatically rescheduled for sending after updating.")])])])],-1),At={class:"flex items-center justify-between"},Dt={class:"ml-auto flex items-center justify-end space-x-4 mt-6"},Wt={__name:"FailedList",props:{data:Object,sequences:Array,filters:Object},setup(u){var A,D;const V=u,y=T({}),b=i(!1),g=i(null),k=i(!1),C=i(!1),r=T({id:null,email:"",reschedule:!0}),x=i(((A=V.filters)==null?void 0:A.sequence_id)||null),f=i(((D=V.filters)==null?void 0:D.search)||""),M=i(!1),q=i(""),L=i(!1),I=i(""),h=i(null),U=s=>{g.value=s,b.value=!0},E=()=>{b.value=!1},H=s=>{g.value=s,k.value=!0},j=()=>{k.value=!1},P=s=>{q.value=s,M.value=!0},$=()=>{M.value=!1},z=()=>{L.value=!1},Z=s=>{x.value=s,F()},F=s=>{s!==void 0&&(f.value=s),y.get(route("failed-emails.index",{search:f.value,sequence_id:x.value}),{preserveState:!0})},G=()=>{y.delete(route("sent-email.destroy",{id:g.value}),{onSuccess:()=>E()})},J=()=>{y.post(route("failed-emails.reschedule",{id:g.value}),{onSuccess:s=>{var t;j();const l=((t=s==null?void 0:s.data)==null?void 0:t.message)||"Email rescheduled successfully";alert(l)}})},K=s=>{h.value=s,r.id=s.id,r.email=s.lead.email,r.reschedule=!0,C.value=!0},v=()=>{C.value=!1,r.reset(),h.value=null},Q=()=>{r.patch(route("failed-emails.update"),{onSuccess:s=>{var t;v();const l=((t=s==null?void 0:s.data)==null?void 0:t.message)||"Email updated successfully";alert(l)}})};return(s,l)=>(n(),c(R,null,[o(_(W),{title:"Failed Emails"}),o(oe,null,{default:a(()=>[e("div",ce,[e("div",ue,[he,e("div",me,[o(w,{for:"search_field"}),e("div",_e,[pe,X(e("input",{type:"text",name:"search",id:"search_field","onUpdate:modelValue":l[0]||(l[0]=t=>f.value=t),onInput:l[1]||(l[1]=t=>F(t.target.value)),class:"block w-full rounded-lg border-0 py-1.5 pl-8 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Search"},null,544),[[Y,f.value]])])])]),e("div",ge,[e("div",xe,[fe,o(w,{for:"customer_id",value:"Filters"})]),e("div",ve,[e("div",we,[o(w,{for:"sequence_id",value:"Sequence"}),e("div",ye,[o(ne,{options:[{id:"",name:"All Sequence"},...u.sequences],modelValue:x.value,"onUpdate:modelValue":l[2]||(l[2]=t=>x.value=t),onOnchange:l[3]||(l[3]=t=>Z(t))},null,8,["options","modelValue"])])])])]),e("div",be,[e("div",ke,[e("table",Ce,[Me,u.data.data&&u.data.data.length>0?(n(),c("tbody",Ee,[(n(!0),c(R,null,ee(u.data.data,t=>(n(),c("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",je,[e("div",null,d(t.lead.first_name??"-")+" "+d(t.lead.last_name??"-"),1),e("div",Be,d(t.lead.email??"-"),1)]),e("td",Se,d(t.sequence.name??"-"),1),e("td",Ve,[e("button",{onClick:B=>P(t.content)},d(t.subject||"-"),9,qe)]),e("td",Le,[e("div",$e,[o(le,{align:"right",width:"48"},{trigger:a(()=>[ze]),content:a(()=>[e("button",{type:"button",onClick:B=>K(t),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Te,8,Fe),e("button",{type:"button",onClick:B=>H(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ie,8,Re),e("button",{type:"button",onClick:B=>U(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ze,8,Ue)]),_:2},1024)])])]))),128))])):(n(),c("tbody",Ge,Ke))])])]),u.data.data&&u.data.data.length>0?(n(),te(ie,{key:0,class:"mt-6",links:u.data.links},null,8,["links"])):N("",!0),o(p,{show:b.value,onClose:E},{default:a(()=>[e("div",Qe,[We,e("div",Xe,[o(S,{onClick:E},{default:a(()=>[m(" Cancel ")]),_:1}),o(ae,{class:"ml-3",onClick:G},{default:a(()=>[m(" Delete ")]),_:1})])])]),_:1},8,["show"]),o(p,{show:k.value,onClose:j},{default:a(()=>[e("div",Ye,[et,tt,e("div",st,[o(S,{onClick:j},{default:a(()=>[m(" Cancel ")]),_:1}),o(O,{class:"ml-3",onClick:J},{default:a(()=>[m(" Reschedule ")]),_:1})])])]),_:1},8,["show"]),o(p,{show:M.value,onClose:$,"max-width":"2xl"},{default:a(()=>[e("div",ot,[e("div",lt,[at,(n(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500",onClick:$},nt))]),e("div",dt,[e("div",{class:"prose prose-sm",innerHTML:q.value||"-"},null,8,rt)])])]),_:1},8,["show"]),o(p,{show:L.value,onClose:z,"max-width":"2xl"},{default:a(()=>[e("div",ct,[e("div",ut,[ht,(n(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500",onClick:z},_t))]),e("div",pt,[e("div",gt,d(I.value),1)])])]),_:1},8,["show"]),o(p,{show:C.value,onClose:v,"max-width":"md"},{default:a(()=>[e("div",xt,[e("div",ft,[vt,(n(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5 absolute top-3 right-3 cursor-pointer text-gray-500 hover:text-red-500",onClick:v},yt))]),e("div",bt,[e("form",{onSubmit:se(Q,["prevent"])},[e("div",Ct,[o(w,{for:"email",value:"Email Address",class:"mb-1"}),o(de,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:_(r).email,"onUpdate:modelValue":l[4]||(l[4]=t=>_(r).email=t),required:"",autocomplete:"email"},null,8,["modelValue"]),o(re,{class:"mt-2",message:_(r).errors.email},null,8,["message"]),Mt]),h.value?(n(),c("div",Et,[e("div",jt,[e("div",Bt,[e("div",null,[St,e("p",Vt,d(h.value.sequence.name),1)]),e("div",null,[qt,e("p",Lt,d(h.value.subject),1)]),e("div",null,[$t,e("p",zt,d(h.value.lead.first_name)+" "+d(h.value.lead.last_name),1)])])])])):N("",!0),Ft,e("div",At,[e("div",Dt,[o(S,{type:"button",onClick:v},{default:a(()=>[m("Cancel")]),_:1}),o(O,{type:"submit",disabled:_(r).processing},{default:a(()=>[m("Update")]),_:1},8,["disabled"])])])],40,kt)])])]),_:1},8,["show"])])]),_:1})],64))}};export{Wt as default};
