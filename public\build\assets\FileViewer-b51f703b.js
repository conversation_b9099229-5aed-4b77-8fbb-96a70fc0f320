import{q as r,o as e,c as t}from"./app-ab313c9b.js";const n={class:"w-full items-center"},a=["src"],c=["src"],p={key:2},f={__name:"FileViewer",props:{fileUrl:{type:String}},setup(s){const l=s,i=r(()=>{const o=l.fileUrl.split(".").pop().toLowerCase();return o==="pdf"?"pdf":["jpg","jpeg","png"].includes(o)?"image":"unsupported"});return(o,m)=>(e(),t("div",n,[i.value==="pdf"?(e(),t("iframe",{key:0,src:s.fileUrl,width:"100%",height:"500px",style:{"max-width":"100%","max-height":"500px","overflow-y":"auto"}},null,8,a)):i.value==="image"?(e(),t("img",{key:1,src:s.fileUrl,alt:"Image",style:{"max-width":"100%","max-height":"500px","overflow-y":"auto"}},null,8,c)):(e(),t("p",p,"No Image"))]))}};export{f as _};
