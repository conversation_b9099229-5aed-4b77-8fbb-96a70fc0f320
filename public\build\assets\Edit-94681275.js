import{T as h,o as b,c as y,a as o,u as s,w as a,F as v,Z as w,b as e,g as u,h as x,k as p,y as f}from"./app-ab313c9b.js";import{_ as j,b as V}from"./AdminLayout-e03e3765.js";import{_ as d}from"./InputLabel-96626de3.js";import{_}from"./TextInput-3a192a65.js";import{_ as n}from"./InputError-fb109721.js";import{P as k}from"./PrimaryButton-a46c6f01.js";import{_ as P}from"./CreateButton-c8ceeae1.js";import"./_plugin-vue_export-helper-c27b6911.js";const U={class:"items-start"},$={class:"flex justify-between items-center mb-6"},B=e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Portfolio Project",-1),S={class:"flex space-x-2"},T={class:"bg-white rounded-lg shadow p-6"},E=["onSubmit"],N={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},D={class:"flex mt-6 items-center justify-between"},L={class:"ml-auto flex items-center justify-end gap-x-6"},M=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Back",-1),G={__name:"Edit",props:["portfolio"],setup(c){const i=c,t=h({project_name:i.portfolio.project_name,url:i.portfolio.url||"",description:i.portfolio.description||"",technology:i.portfolio.technology||""}),g=()=>{t.patch(route("portfolios.update",i.portfolio.id))};return(m,l)=>(b(),y(v,null,[o(s(w),{title:"Portfolio"}),o(j,null,{default:a(()=>[e("div",U,[e("div",$,[B,e("div",S,[o(P,{href:m.route("portfolios.show",c.portfolio.id)},{default:a(()=>[u(" View Project ")]),_:1},8,["href"])])]),e("div",T,[e("form",{onSubmit:x(g,["prevent"]),class:"space-y-6"},[e("div",N,[e("div",null,[o(d,{for:"project_name",value:"Project Name *"}),o(_,{id:"project_name",modelValue:s(t).project_name,"onUpdate:modelValue":l[0]||(l[0]=r=>s(t).project_name=r),type:"text",class:"mt-1 block w-full",required:"",autofocus:"",placeholder:"Enter project name"},null,8,["modelValue"]),o(n,{class:"mt-2",message:s(t).errors.project_name},null,8,["message"])]),e("div",null,[o(d,{for:"url",value:"Project URL"}),o(_,{id:"url",modelValue:s(t).url,"onUpdate:modelValue":l[1]||(l[1]=r=>s(t).url=r),type:"url",class:"mt-1 block w-full",placeholder:"https://example.com"},null,8,["modelValue"]),o(n,{class:"mt-2",message:s(t).errors.url},null,8,["message"])]),e("div",null,[o(d,{for:"description",value:"Description"}),p(e("textarea",{id:"description","onUpdate:modelValue":l[2]||(l[2]=r=>s(t).description=r),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Describe your project..."},null,512),[[f,s(t).description]]),o(n,{class:"mt-2",message:s(t).errors.description},null,8,["message"])]),e("div",null,[o(d,{for:"technology",value:"Technologies Used"}),p(e("textarea",{id:"technology","onUpdate:modelValue":l[3]||(l[3]=r=>s(t).technology=r),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"e.g., Laravel, Vue.js, MySQL, Tailwind CSS"},null,512),[[f,s(t).technology]]),o(n,{class:"mt-2",message:s(t).errors.technology},null,8,["message"])])]),e("div",D,[e("div",L,[o(V,{href:m.route("portfolios.index")},{svg:a(()=>[M]),_:1},8,["href"]),o(k,{disabled:s(t).processing},{default:a(()=>[u("Update")]),_:1},8,["disabled"])])])],40,E)])])]),_:1})],64))}};export{G as default};
