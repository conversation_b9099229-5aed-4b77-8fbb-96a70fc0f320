import{_ as m}from"./AdminLayout-e03e3765.js";import{o as u,c as p,a as s,u as n,w as a,F as _,Z as g,b as t,j as h,g as l,t as d,O as f}from"./app-ab313c9b.js";import{P as x}from"./PrimaryButton-a46c6f01.js";import{_ as v}from"./SecondaryButton-cda7d669.js";import"./_plugin-vue_export-helper-c27b6911.js";const y={class:"animate-top"},b={class:"flex justify-between items-center mb-6"},w=t("div",null,[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Template Details")],-1),T={class:"grid grid-cols-1 lg:grid-cols-4 gap-6"},k={class:"lg:col-span-3"},j={class:"bg-white shadow rounded-lg p-6"},B={class:"space-y-6"},C=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Subject",-1),$={class:"text-gray-700 bg-gray-50 p-3 rounded-md"},D=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Content",-1),N={class:"bg-gray-50 p-4 rounded-md"},S=["innerHTML"],V=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Raw Content",-1),A={class:"bg-gray-100 p-4 rounded-md text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto"},E={class:"lg:col-span-1 space-y-6"},F={class:"bg-white shadow rounded-lg p-6"},H=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions",-1),L={class:"space-y-3"},q={__name:"Show",props:["template"],setup(e){const c=e,r=()=>{confirm("Are you sure you want to delete this template?")&&f.delete(route("templates.destroy",c.template.id))};return(o,i)=>(u(),p(_,null,[s(n(g),{title:`Template: ${e.template.subject}`},null,8,["title"]),s(m,null,{default:a(()=>[t("div",y,[t("div",b,[w,t("div",null,[s(n(h),{href:o.route("templates.index"),class:"text-sm text-gray-600 hover:text-gray-900"},{default:a(()=>[l(" ← Back to Templates ")]),_:1},8,["href"])])]),t("div",T,[t("div",k,[t("div",j,[t("div",B,[t("div",null,[C,t("p",$,d(e.template.subject),1)]),t("div",null,[D,t("div",N,[t("div",{class:"prose prose-sm max-w-none",innerHTML:e.template.content},null,8,S)])]),t("div",null,[V,t("pre",A,d(e.template.content),1)])])])]),t("div",E,[t("div",F,[H,t("div",L,[s(x,{class:"w-full",onClick:i[0]||(i[0]=M=>o.$inertia.visit(o.route("templates.edit",e.template.id)))},{default:a(()=>[l(" Edit Template ")]),_:1}),s(v,{class:"w-full text-red-600 border-red-300 hover:bg-red-50",onClick:r},{default:a(()=>[l(" Delete Template ")]),_:1})])])])])])]),_:1})],64))}};export{q as default};
