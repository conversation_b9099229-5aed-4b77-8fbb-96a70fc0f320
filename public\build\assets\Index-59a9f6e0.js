import{r as p,x as y,o as s,c as l,a as r,u as h,w as d,F as x,Z as b,b as e,k as w,y as k,g as _,d as j,f as c,O as v,t as u,j as m,e as V,p as I,m as B}from"./app-ab313c9b.js";import{_ as C}from"./AdminLayout-e03e3765.js";import{_ as M}from"./Pagination-bae10c2c.js";import{_ as N}from"./CreateButton-c8ceeae1.js";import{_ as S}from"./_plugin-vue_export-helper-c27b6911.js";const a=o=>(I("data-v-4c14fed7"),o=o(),B(),o),P={class:"animate-top"},D={class:"flex justify-between items-center"},$=a(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Portfolio")],-1)),A={class:"flex justify-end"},T={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},z={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},E=a(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),F={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},H={class:"flex justify-end"},O={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8"},G={class:"p-6"},L={class:"flex justify-between items-start mb-4"},U={class:"text-lg font-semibold text-gray-900 truncate"},Z={class:"flex space-x-2"},q=["onClick"],J={key:0,class:"mb-2"},K=["href"],Q={key:1,class:"text-gray-500 text-sm mb-2 line-clamp-3"},R={key:2,class:"mb-2 flex items-center"},W=a(()=>e("p",{class:"text-xs text-gray-500"},"Technologies:",-1)),X={class:"text-sm text-gray-700 ml-2"},Y={class:"flex justify-between items-center"},ee=a(()=>e("div",{class:"text-xs text-gray-500"},null,-1)),te={class:"flex space-x-2"},se={key:1,class:"bg-white rounded-lg shadow p-12 text-center mt-8"},oe=a(()=>e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1)),le=a(()=>e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No portfolio items",-1)),ae=a(()=>e("p",{class:"mt-1 text-sm text-gray-500"},"Get started by creating your first portfolio project.",-1)),re=a(()=>e("div",{class:"mt-6"},null,-1)),ie=[oe,le,ae,re],de={key:2,class:"mt-6"},ce={__name:"Index",props:["portfolios","filters"],setup(o){const n=p(o.filters.search||"");y([n],()=>{v.get(route("portfolios.index"),{search:n.value},{preserveState:!0,replace:!0})},{debounce:300});const g=i=>{confirm("Are you sure you want to delete this portfolio item?")&&v.delete(route("portfolios.destroy",i))};return(i,f)=>(s(),l(x,null,[r(h(b),{title:"Portfolio"}),r(C,null,{default:d(()=>[e("div",P,[e("div",D,[$,e("div",A,[e("div",T,[e("div",z,[E,w(e("input",{id:"search-field","onUpdate:modelValue":f[0]||(f[0]=t=>n.value=t),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,512),[[k,n.value]])])]),e("div",F,[e("div",H,[r(N,{href:i.route("portfolios.create")},{default:d(()=>[_(" Add New Project ")]),_:1},8,["href"])])])])]),o.portfolios.data.length>0?(s(),l("div",O,[(s(!0),l(x,null,j(o.portfolios.data,t=>(s(),l("div",{key:t.id,class:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"},[e("div",G,[e("div",L,[e("h3",U,u(t.project_name),1),e("div",Z,[r(h(m),{href:i.route("portfolios.edit",t.id),class:"text-blue-600 font-semibold hover:text-blue-800 text-sm"},{default:d(()=>[_(" Edit ")]),_:2},1032,["href"]),e("button",{onClick:he=>g(t.id),class:"text-red-600 font-semibold hover:text-red-800 text-sm"}," Delete ",8,q)])]),t.url?(s(),l("div",J,[e("a",{href:t.url,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm break-all"},u(t.url),9,K)])):c("",!0),t.description?(s(),l("p",Q,u(t.description),1)):c("",!0),t.technology?(s(),l("div",R,[W,e("p",X,u(t.technology),1)])):c("",!0),e("div",Y,[ee,e("div",te,[t.url?(s(),V(h(m),{key:0,href:t.url,target:"_blank",class:"text-blue-600 font-semibold hover:text-blue-800 text-sm"},{default:d(()=>[_(" View Project → ")]),_:2},1032,["href"])):c("",!0),r(h(m),{href:i.route("portfolios.show",t.id),class:"text-gray-600 font-semibold hover:text-gray-800 text-sm"},{default:d(()=>[_(" Details ")]),_:2},1032,["href"])])])])]))),128))])):(s(),l("div",se,ie)),o.portfolios.data.length>0?(s(),l("div",de,[r(M,{links:o.portfolios.links},null,8,["links"])])):c("",!0)])]),_:1})],64))}},ve=S(ce,[["__scopeId","data-v-4c14fed7"]]);export{ve as default};
