import{T as p,r as f,l as h,o as n,c as d,a as i,w as b,F as _,b as e,h as g,t as c,f as w,g as v}from"./app-ab313c9b.js";import{_ as y}from"./AdminLayout-e03e3765.js";const k={class:"p-8 bg-white rounded-xl shadow-lg max-w-md mx-auto border border-gray-100"},L=e("div",{class:"text-center mb-8"},[e("div",{class:"mx-auto bg-blue-50 w-16 h-16 rounded-full flex items-center justify-center mb-4"}," 📁 "),e("h2",{class:"text-2xl font-bold text-gray-800 mb-2"},"Upload Leads"),e("p",{class:"text-gray-500 text-sm"},"Supported formats: .xls, .xlsx")],-1),S=["onSubmit"],C={class:"flex items-center justify-center w-full"},j={class:"flex flex-col items-center justify-center w-full h-48 border-2 border-dashed border-gray-300 rounded-xl hover:border-blue-500 hover:bg-gray-50 transition-all cursor-pointer"},X=e("div",{class:"flex flex-col items-center justify-center pt-5 pb-6"},[e("svg",{class:"w-8 h-8 mb-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),e("p",{class:"mb-2 text-sm text-gray-500"},[e("span",{class:"font-semibold"},"Click to upload"),v(" or drag and drop ")]),e("p",{class:"text-xs text-gray-400"},"XLS/XLSX files only")],-1),B={key:0,class:"p-4 bg-gray-50 rounded-lg border border-gray-200"},F={class:"flex items-center space-x-3"},U=e("div",{class:"flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"}," 📄 ",-1),M={class:"flex-1 min-w-0"},N={class:"text-sm font-medium text-gray-900 truncate"},E={class:"text-xs text-gray-500"},V=e("button",{type:"submit",class:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"},[e("span",null,"Upload File"),e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"})])],-1),T=e("p",{class:"text-center mt-6 text-sm text-gray-500"}," Max file size: 10MB • 1000 records max ",-1),H={__name:"Upload",setup(z){const r=p({file:null}),o=f(null),m=l=>{r.file=l.target.files[0],o.value=r.file},u=()=>{r.post(route("leads.importXls"))},x=()=>{const l="sample-leads.xlsx";fetch("/sample-xls",{method:"GET"}).then(t=>{if(!t.ok)throw new Error("Network response was not ok");return t.blob()}).then(t=>{const a=window.URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.setAttribute("download",l),document.body.appendChild(s),s.click(),document.body.removeChild(s)}).catch(t=>{console.error("Error downloading sample leads XLS:",t)})};return(l,t)=>{const a=h("Head");return n(),d(_,null,[i(a,{title:"Upload XLS"}),i(y,null,{default:b(()=>[e("div",k,[L,e("div",{class:"text-center mb-6"},[e("button",{onClick:x,class:"inline-flex items-center px-4 py-2 bg-green-500 text-white text-sm font-medium rounded-lg shadow hover:bg-green-600 transition-all"}," Download Sample XLS ")]),e("form",{onSubmit:g(u,["prevent"]),enctype:"multipart/form-data",class:"space-y-6"},[e("div",C,[e("label",j,[X,e("input",{type:"file",onChange:m,accept:".xls,.xlsx",class:"hidden"},null,32)])]),o.value?(n(),d("div",B,[e("div",F,[U,e("div",M,[e("p",N,c(o.value.name),1),e("p",E,c((o.value.size/1024).toFixed(2))+" KB",1)])])])):w("",!0),V],40,S),T])]),_:1})],64)}}};export{H as default};
