import{_ as h}from"./AdminLayout-e03e3765.js";import{o as s,c as i,a as n,u as r,w as l,F as m,Z as _,b as t,j as g,g as d,t as o,f as a}from"./app-ab313c9b.js";const f={class:"items-start"},x={class:"flex justify-between items-center mb-6"},u=t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Project Details",-1),y={class:"flex space-x-2"},p={class:"bg-white rounded-lg shadow overflow-hidden"},v={class:"px-6 py-4 border-b border-gray-200"},b={class:"flex justify-between items-start"},w={class:"text-xl font-semibold text-gray-900"},k={class:"text-sm text-gray-500 mt-1"},D={key:0,class:"text-sm text-gray-500"},j={key:0,class:"flex-shrink-0"},S=["href"],L=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1),P={class:"px-6 py-6"},B={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},C={class:"lg:col-span-2 space-y-6"},U={key:0},V=t("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Description",-1),N={class:"max-w-none"},F={class:"text-sm text-gray-700 leading-relaxed whitespace-pre-wrap"},M={key:1},T=t("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Technologies Used",-1),E={class:"max-w-none"},H={class:"text-sm text-gray-700 leading-relaxed whitespace-pre-wrap"},R={class:"lg:col-span-1"},Z={class:"bg-gray-50 rounded-lg p-4 space-y-4"},$=t("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Project Details",-1),q={key:0},z=t("dt",{class:"text-sm font-medium text-gray-500"},"Project URL",-1),A={class:"mt-1"},G=["href"],I=t("dt",{class:"text-sm font-medium text-gray-500"},"Created",-1),J={class:"mt-1 text-sm text-gray-900"},W={__name:"Show",props:["portfolio"],setup(e){return(c,K)=>(s(),i(m,null,[n(r(_),{title:"Portfolio"}),n(h,null,{default:l(()=>[t("div",f,[t("div",x,[u,t("div",y,[n(r(g),{href:c.route("portfolios.index"),class:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"},{default:l(()=>[d(" Back to Portfolio ")]),_:1},8,["href"])])]),t("div",p,[t("div",v,[t("div",b,[t("div",null,[t("h2",w,o(e.portfolio.project_name),1),t("p",k," Created on "+o(new Date(e.portfolio.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})),1),e.portfolio.updated_at!==e.portfolio.created_at?(s(),i("p",D," Last updated on "+o(new Date(e.portfolio.updated_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})),1)):a("",!0)]),e.portfolio.url?(s(),i("div",j,[t("a",{href:e.portfolio.url,target:"_blank",class:"inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors"},[L,d(" View Live Project ")],8,S)])):a("",!0)])]),t("div",P,[t("div",B,[t("div",C,[e.portfolio.description?(s(),i("div",U,[V,t("div",N,[t("p",F,o(e.portfolio.description),1)])])):a("",!0),e.portfolio.technology?(s(),i("div",M,[T,t("div",E,[t("p",H,o(e.portfolio.technology),1)])])):a("",!0)]),t("div",R,[t("div",Z,[$,e.portfolio.url?(s(),i("div",q,[z,t("dd",A,[t("a",{href:e.portfolio.url,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm break-all"},o(e.portfolio.url),9,G)])])):a("",!0),t("div",null,[I,t("dd",J,o(new Date(e.portfolio.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})),1)])])])])])])])]),_:1})],64))}};export{W as default};
