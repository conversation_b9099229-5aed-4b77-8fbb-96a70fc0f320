import{_ as v,a as b,b as k}from"./AdminLayout-e03e3765.js";import{_ as C}from"./CreateButton-c8ceeae1.js";import{_ as M}from"./SecondaryButton-cda7d669.js";import{D as L}from"./DangerButton-a8df982e.js";import{M as B}from"./Modal-b5ccb299.js";import{_ as S}from"./SwitchButton-857b1265.js";import{_ as V}from"./Pagination-bae10c2c.js";import{T as $,r as p,o as n,c as i,a as s,u as j,w as o,F as f,Z as A,b as t,g as u,d as N,e as U,f as z,t as h}from"./app-ab313c9b.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const I={class:"animate-top"},E={class:"flex justify-between items-center"},T=t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Users")],-1),O={class:"flex justify-end"},F={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},H={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},R=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),Z={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},q={class:"flex justify-end"},G={class:"mt-8 overflow-x-auto sm:rounded-lg"},J={class:"shadow sm:rounded-lg"},K={class:"w-full text-sm text-left rtl:text-right text-gray-500"},P=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," NAME "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," EMAIL "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," ROLE "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," STATUS "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," ACTION ")])],-1),Q={key:0},W={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},X={class:"px-4 py-2.5 min-w-36"},Y=t("td",{class:"px-4 py-2.5 min-w-36"},null,-1),D={class:"items-center px-4 py-2.5"},tt={class:"items-center px-4 py-2.5"},et={class:"flex items-center justify-start gap-4"},st=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),ot=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),at=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),lt=["onClick"],nt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),it=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),dt=[nt,it],rt={key:1},ct=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),mt=[ct],ut={class:"p-6"},ht=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),_t={class:"mt-6 flex justify-end"},Bt={__name:"List",props:["data","search"],setup(l){const r=$({}),c=p(!1),_=p(null),x=a=>{_.value=a,c.value=!0},m=()=>{c.value=!1},g=()=>{r.delete(route("users.destroy",{id:_.value}),{onSuccess:()=>m()})},y=a=>{r.get(route("users.index",{search:a}),{preserveState:!0})},w=(a,d)=>{r.post(route("users.activation",{id:d,status:a}),{})};return(a,d)=>(n(),i(f,null,[s(j(A),{title:"Users"}),s(v,null,{default:o(()=>[t("div",I,[t("div",E,[T,t("div",O,[t("div",F,[t("div",H,[R,t("input",{id:"search-field",onInput:d[0]||(d[0]=e=>y(e.target.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),t("div",Z,[t("div",q,[s(C,{href:a.route("users.create")},{default:o(()=>[u(" Add User ")]),_:1},8,["href"])])])])]),t("div",G,[t("div",J,[t("table",K,[P,l.data.data&&l.data.data.length>0?(n(),i("tbody",Q,[(n(!0),i(f,null,N(l.data.data,(e,pt)=>(n(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",W,h(e.first_name)+" "+h(e.last_name),1),t("td",X,h(e.email),1),Y,t("td",D,[s(S,{switchValue:e.status,userId:e.id,onUpdateSwitchValue:w},null,8,["switchValue","userId"])]),t("td",tt,[t("div",et,[s(b,{align:"right",width:"48"},{trigger:o(()=>[st]),content:o(()=>[s(k,{href:a.route("users.edit",{id:e.id})},{svg:o(()=>[ot]),text:o(()=>[at]),_:2},1032,["href"]),t("button",{type:"button",onClick:ft=>x(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},dt,8,lt)]),_:2},1024)])])]))),128))])):(n(),i("tbody",rt,mt))])])]),l.data.data&&l.data.data.length>0?(n(),U(V,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):z("",!0)]),s(B,{show:c.value,onClose:m},{default:o(()=>[t("div",ut,[ht,t("div",_t,[s(M,{onClick:m},{default:o(()=>[u(" Cancel ")]),_:1}),s(L,{class:"ml-3",onClick:g},{default:o(()=>[u(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Bt as default};
