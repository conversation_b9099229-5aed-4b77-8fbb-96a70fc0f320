import{r as g,x as y,o as a,c as i,a as c,u as w,w as l,F as u,Z as v,b as t,k as b,y as k,g as C,d as $,e as j,f as B,O as h,t as p}from"./app-ab313c9b.js";import{_ as S,a as T,b as L}from"./AdminLayout-e03e3765.js";import{_ as M}from"./Pagination-bae10c2c.js";import{_ as z}from"./CreateButton-c8ceeae1.js";const D={class:"animate-top"},N={class:"flex justify-between items-center"},A=t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Templates")],-1),V={class:"flex justify-end"},O={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},F={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},E=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),I={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},H={class:"flex justify-end"},U={class:"mt-8 overflow-x-auto sm:rounded-lg"},Y={class:"shadow sm:rounded-lg"},Z={class:"w-full text-sm text-left rtl:text-right text-gray-500"},q=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Subject "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Content "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Created At "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," ACTION ")])],-1),G={key:0},J=["onClick"],K=["onClick"],P=["onClick"],Q={class:"items-center px-4 py-2.5"},R={class:"flex items-center justify-start gap-4"},W=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),X=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),tt=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),et=["onClick"],st=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),ot=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),at=[st,ot],lt={key:1},nt=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),rt=[nt],pt={__name:"Index",props:["templates","filters"],setup(n){const d=g(n.filters.search||"");y([d],()=>{h.get(route("templates.index"),{search:d.value},{preserveState:!0,replace:!0})},{debounce:300});const _=s=>{confirm("Are you sure you want to delete this template?")&&h.delete(route("templates.destroy",s))},m=s=>{h.visit(route("templates.show",s))},x=s=>{if(!s)return"-";const o=new Date(s),e=String(o.getDate()).padStart(2,"0"),r=String(o.getMonth()+1).padStart(2,"0"),f=o.getFullYear();return`${e}-${r}-${f}`};return(s,o)=>(a(),i(u,null,[c(w(v),{title:"Templates"}),c(S,null,{default:l(()=>[t("div",D,[t("div",N,[A,t("div",V,[t("div",O,[t("div",F,[E,b(t("input",{id:"search-field","onUpdate:modelValue":o[0]||(o[0]=e=>d.value=e),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search templates...",type:"search",name:"search"},null,512),[[k,d.value]])])]),t("div",I,[t("div",H,[c(z,{href:s.route("templates.create")},{default:l(()=>[C(" Add Template ")]),_:1},8,["href"])])])])]),t("div",U,[t("div",Y,[t("table",Z,[q,n.templates.data.length>0?(a(),i("tbody",G,[(a(!0),i(u,null,$(n.templates.data,e=>(a(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b cursor-pointer",key:e.id},[t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36",onClick:r=>m(e.id)},p(e.subject),9,J),t("td",{class:"px-4 py-2.5",onClick:r=>m(e.id)},p(e.content),9,K),t("td",{class:"px-4 py-2.5 min-w-36",onClick:r=>m(e.id)},p(x(e.created_at)),9,P),t("td",Q,[t("div",R,[c(T,{align:"right",width:"48"},{trigger:l(()=>[W]),content:l(()=>[c(L,{href:s.route("templates.edit",e.id)},{svg:l(()=>[X]),text:l(()=>[tt]),_:2},1032,["href"]),t("button",{type:"button",onClick:r=>_(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},at,8,et)]),_:2},1024)])])]))),128))])):(a(),i("tbody",lt,rt))])])]),n.templates.links.length>0?(a(),j(M,{key:0,class:"mt-6",links:n.templates.links},null,8,["links"])):B("",!0)])]),_:1})],64))}};export{pt as default};
