<script setup>
import { ref, onMounted, watch, computed, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import InputLabel from '@/Components/InputLabel.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

const props = defineProps({
  prospects: Object,
  filters: Object,
  filterOptions: Object,
});

const searchValue = ref(props.filters?.search || '');
const status = ref(props.filters?.status || '');
const priority = ref(props.filters?.priority || '');
const leadSource = ref(props.filters?.lead_source || '');
const assignedTo = ref(props.filters?.assigned_to || '');

const form = useForm({});

const modalVisible = ref(false);
const selectedId = ref(null);

// Watch for filter changes and update URL
watch([searchValue, status, priority, leadSource, assignedTo], () => {
  router.get(route('prospects.index'), {
    search: searchValue.value,
    status: status.value,
    priority: priority.value,
    lead_source: leadSource.value,
    assigned_to: assignedTo.value,
  }, {
    preserveState: true,
    replace: true,
  });
}, { debounce: 300 });

const deleteProspect = (id) => {
  selectedId.value = id;
  modalVisible.value = true;
};

const confirmDelete = () => {
  form.delete(route('prospects.destroy', selectedId.value), {
    onSuccess: () => {
      modalVisible.value = false;
      selectedId.value = null;
    },
  });
};

const convertToLead = (id) => {
  form.post(route('prospects.convert', id), {
    onSuccess: () => {
      // Handle success
    },
  });
};

const getStatusBadgeClass = (status) => {
  const classes = {
    'new': 'bg-blue-100 text-blue-800',
    'contacted': 'bg-yellow-100 text-yellow-800',
    'qualified': 'bg-green-100 text-green-800',
    'unqualified': 'bg-red-100 text-red-800',
    'converted': 'bg-purple-100 text-purple-800',
    'lost': 'bg-red-100 text-red-800',
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const getPriorityBadgeClass = (priority) => {
  const classes = {
    'low': 'bg-gray-100 text-gray-800',
    'medium': 'bg-blue-100 text-blue-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800',
  };
  return classes[priority] || 'bg-gray-100 text-gray-800';
};

// const formatDate = (date) => {
//   if (!date) return '-';
//   return new Date(date).toLocaleDateString();
// };

const formatDate = (date) => {
  if (!date) return '-';

  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = d.getFullYear();

  return `${day}-${month}-${year}`;
};

const formatCurrency = (amount) => {
  if (!amount) return '-';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const goToProspect = (id) => {
  router.visit(route('prospects.show', id));
};

</script>

<template>
  <Head title="Prospects" />

  <AdminLayout>
    <div class="animate-top">
        <div class="flex justify-between items-center">
            <div class="items-start">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Prospects</h1>
            </div>
            <div class="flex justify-end">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                        </svg>
                        <input id="search-field"  v-model="searchValue" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                    </div>
                </div>
                <div class="mt-4 sm:ml-6 sm:mt-0 sm:flex-none">
                    <div class="flex justify-end">
                        <CreateButton :href="route('prospects.create')">
                                Add New Prospect
                        </CreateButton>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex justify-between items-center mb-2">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" class="ml-2" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <!-- Status Filter -->
              <div>
                <InputLabel for="status" value="Status" />
                <select
                  id="status"
                  v-model="status"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="">All Statuses</option>
                  <option v-for="statusOption in filterOptions.statuses" :key="statusOption" :value="statusOption">
                    {{ statusOption.charAt(0).toUpperCase() + statusOption.slice(1) }}
                  </option>
                </select>
              </div>

              <!-- Priority Filter -->
              <div>
                <InputLabel for="priority" value="Priority" />
                <select
                  id="priority"
                  v-model="priority"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="">All Priorities</option>
                  <option v-for="priorityOption in filterOptions.priorities" :key="priorityOption" :value="priorityOption">
                    {{ priorityOption.charAt(0).toUpperCase() + priorityOption.slice(1) }}
                  </option>
                </select>
              </div>

              <!-- Lead Source Filter -->
              <div>
                <InputLabel for="lead_source" value="Lead Source" />
                <select
                  id="lead_source"
                  v-model="leadSource"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="">All Sources</option>
                  <option v-for="sourceOption in filterOptions.leadSources" :key="sourceOption" :value="sourceOption">
                    {{ sourceOption.charAt(0).toUpperCase() + sourceOption.slice(1).replace('_', ' ') }}
                  </option>
                </select>
              </div>

              <!-- Assigned To Filter -->
              <div>
                <InputLabel for="assigned_to" value="Assigned To" />
                <select
                  id="assigned_to"
                  v-model="assignedTo"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="">All Users</option>
                  <option v-for="user in filterOptions.users" :key="user.id" :value="user.id">
                    {{ user.name }}
                  </option>
                </select>
              </div>
            </div>
        </div>

        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                            Prospect
                            </th>
                            <!-- <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                            COMPANY
                            </th> -->
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                            Source
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                            Status
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                            Priority
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                            Follow Up
                            </th>
                            <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">
                                ACTION
                            </th>
                        </tr>
                    </thead>
                    <tbody v-if="prospects.data.length > 0">
                        <tr class="odd:bg-white even:bg-gray-50 border-b cursor-pointer" v-for="prospect in prospects.data" :key="prospect.id">
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"  @click="goToProspect(prospect.id)">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ prospect.first_name }} {{ prospect.last_name }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ prospect.email }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <!-- <td class="px-4 py-2.5 min-w-36"  @click="goToProspect(prospect.id)">
                                <div class="text-sm text-gray-900">{{ prospect.company || '-' }}</div>
                                <div class="text-sm text-gray-500">{{ prospect.position || '-' }}</div>
                            </td> -->
                            <td class="px-4 py-2.5 min-w-36"  @click="goToProspect(prospect.id)">
                                <span class="text-sm text-gray-900">
                                    {{ prospect.lead_source.charAt(0).toUpperCase() + prospect.lead_source.slice(1).replace('_', ' ') }}
                                </span>
                            </td>
                            <td class="px-4 py-2.5 min-w-36"  @click="goToProspect(prospect.id)">
                                <span :class="getStatusBadgeClass(prospect.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                    {{ prospect.status.charAt(0).toUpperCase() + prospect.status.slice(1) }}
                                </span>
                            </td>
                            <td class="px-4 py-2.5 min-w-36"  @click="goToProspect(prospect.id)">
                                <span :class="getPriorityBadgeClass(prospect.priority)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                    {{ prospect.priority.charAt(0).toUpperCase() + prospect.priority.slice(1) }}
                                </span>
                            </td>
                            <td class="px-4 py-2.5 min-w-36"  @click="goToProspect(prospect.id)">
                                {{ formatDate(prospect.next_follow_up_at) }}
                            </td>
                            <td class="items-center px-4 py-2.5">
                                <div class="flex items-center justify-start gap-4">
                                    <Dropdown align="right" width="48">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <!-- <ActionLink :href="route('prospects.show', prospect.id)">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="View">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12s3.75-6.75 9.75-6.75S21.75 12 21.75 12s-3.75 6.75-9.75 6.75S2.25 12 2.25 12z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        View
                                                    </span>
                                                </template>
                                            </ActionLink> -->
                                            <ActionLink :href="route('prospects.edit', prospect.id)">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                            />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <button type="button" @click="deleteProspect(prospect.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <Pagination v-if="(prospects.links.length > 0)" class="mt-6" :links="prospects.links"></Pagination>
    </div>

    <!-- Delete Confirmation Modal -->
    <Modal :show="modalVisible" @close="modalVisible = false">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Delete Prospect
        </h2>
        <p class="mt-1 text-sm text-gray-600">
          Are you sure you want to delete this prospect? This action cannot be undone.
        </p>
        <div class="mt-6 flex justify-end space-x-3">
          <SecondaryButton @click="modalVisible = false">
            Cancel
          </SecondaryButton>
          <DangerButton @click="confirmDelete" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
            Delete Prospect
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AdminLayout>
</template>
<style scoped>
select {
    padding: 6px  !important;
}
</style>
